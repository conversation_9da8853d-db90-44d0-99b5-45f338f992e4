'use client';

import { UnderlinedTabs } from '@/components/dashboard/underlined-tabs';
import { AlertTriangle, Edit, FileText, Home, MessageSquare, Settings, Shield, Users } from 'lucide-react';

interface HubNavigationTabsProps {
  hubId: string;
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
}

export function HubNavigationTabs({
  hubId,
  currentTab,
  canModerate = false,
  canEdit = false,
}: HubNavigationTabsProps) {
  return (
    <div className="px-6">
      <UnderlinedTabs
        defaultValue={currentTab}
        className="w-full"
        navigational={true}
        tabs={[
          {
            value: 'overview',
            label: 'Overview',
            color: 'indigo' as const,
            icon: <MessageSquare className="h-4 w-4" />,
            href: `/dashboard/hubs/${hubId}`,
          },
        // Only show edit tab if user can edit
        ...(canEdit
          ? [
              {
                value: 'edit',
                label: 'Manage Hub',
                color: 'blue' as const,
                icon: <Edit className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/edit`,
              },
            ]
          : []),
        // Only show members tab if user can moderate
        ...(canModerate
          ? [
              {
                value: 'members',
                label: 'Members',
                color: 'blue' as const,
                icon: <Users className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/members`,
              },
            ]
          : []),
        // Only show connections tab if user can moderate
        ...(canModerate
          ? [
              {
                value: 'connections',
                label: 'Connections',
                color: 'green' as const,
                icon: <Home className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/connections`,
              },
            ]
          : []),
        // Add moderation tab if user has permission
        ...(canModerate
          ? [
              {
                value: 'moderation',
                label: 'Moderation',
                color: 'purple' as const,
                icon: <Shield className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/moderation`,
              },
              {
                value: 'anti-swear',
                label: 'Anti-Swear',
                color: 'red' as const,
                icon: <AlertTriangle className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/anti-swear`,
              },
              {
                value: 'infractions',
                label: 'Infractions',
                color: 'pink' as const,
                icon: <Shield className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/infractions`,
              },
            ]
          : []),
        // Add logging tab if user can edit
        ...(canEdit
          ? [
              {
                value: 'logging',
                label: 'Logging',
                color: 'purple' as const,
                icon: <FileText className="h-4 w-4" />,
                href: `/dashboard/hubs/${hubId}/logging`,
              },
            ]
          : []),
        {
          value: 'settings',
          label: 'Settings',
          color: 'orange' as const,
          icon: <Settings className="h-4 w-4" />,
          href: `/dashboard/hubs/${hubId}/settings`,
        },
      ]}
      />
    </div>
  );
}

// Loading skeleton version of the tabs
export function HubNavigationTabsSkeleton({ currentTab = 'overview' }: { currentTab?: string }) {
  // Define tab colors for the active state
  const tabColors: Record<string, { borderColor: string; textColor: string }> = {
    overview: { borderColor: '#6366f1', textColor: '#818cf8' }, // indigo
    members: { borderColor: '#3b82f6', textColor: '#60a5fa' }, // blue
    connections: { borderColor: '#10b981', textColor: '#34d399' }, // green
    settings: { borderColor: '#10b981', textColor: '#34d399' }, // green
    edit: { borderColor: '#3b82f6', textColor: '#60a5fa' }, // blue
    moderation: { borderColor: '#8b5cf6', textColor: '#a78bfa' }, // purple
    logging: { borderColor: '#f59e0b', textColor: '#fbbf24' }, // amber
  };

  return (
    <div className="px-6">
      <div className="overflow-x-auto -mx-6 px-0 border-b border-gray-800/50 bg-gray-900/80 backdrop-blur-md z-10 no-scrollbar shadow-sm transition-all duration-200">
        <div className="px-6 w-full">
        <div className="w-full flex flex-nowrap justify-center gap-6 bg-transparent max-w-screen-xl mx-auto h-auto p-0 rounded-none">
          {[
            {
              value: 'overview',
              icon: <MessageSquare className="h-4 w-4" />,
              label: 'Overview',
            },
            {
              value: 'edit',
              icon: <Edit className="h-4 w-4" />,
              label: 'Manage Hub',
            },
            {
              value: 'members',
              icon: <Users className="h-4 w-4" />,
              label: 'Members',
            },
            {
              value: 'connections',
              icon: <Home className="h-4 w-4" />,
              label: 'Connections',
            },
            {
              value: 'moderation',
              icon: <Shield className="h-4 w-4" />,
              label: 'Moderation',
            },
            {
              value: 'logging',
              icon: <FileText className="h-4 w-4" />,
              label: 'Logging',
            },
            {
              value: 'settings',
              icon: <Settings className="h-4 w-4" />,
              label: 'Settings',
            },
          ].map((tab) => {
            const isActive = tab.value === currentTab;
            const activeColor = tabColors[tab.value] || tabColors.overview;

            return (
              <div
                key={tab.value}
                className={`px-6 py-4 text-sm font-medium border-b-[2px] transition-all duration-200 flex items-center ${isActive ? `border-[${activeColor.borderColor}] text-[${activeColor.textColor}]` : 'border-transparent text-gray-500'}`}
                style={
                  isActive
                    ? {
                        borderColor: activeColor.borderColor,
                        color: activeColor.textColor,
                      }
                    : {}
                }
              >
                <span className="mr-2">{tab.icon}</span>
                <span>{tab.label}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
    </div>
  );
}
