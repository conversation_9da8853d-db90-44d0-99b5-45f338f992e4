import { auth } from "@/auth";
import { DeleteHubDialog } from '@/components/dashboard/hubs/delete-hub-dialog';
import { HubNavigationTabs } from '@/components/dashboard/hubs/hub-navigation-tabs';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  Edit3,
  Palette,
  Globe,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { notFound, redirect } from "next/navigation";
import type { Metadata } from "next";
import prisma from "@/lib/prisma";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import { HubEditForm } from "./components/hub-edit-form";

interface HubEditPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export async function generateMetadata({
  params,
}: HubEditPageProps): Promise<Metadata> {
  const { hubId } = await params;
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: { name: true },
  });

  return {
    title: hub ? `Edit ${hub.name} | InterChat Dashboard` : "Edit Hub | InterChat Dashboard",
    description: "Manage your InterChat hub settings, appearance, and configuration",
  };
}

export default async function HubEditPage({
  params,
}: HubEditPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/edit`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const isOwner = permissionLevel === PermissionLevel.OWNER;

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  if (!canEdit) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  // Fetch hub data with all necessary relations
  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    include: {
      tags: {
        select: { name: true }
      },
      _count: {
        select: {
          connections: {
            where: { connected: true }
          }
        }
      }
    },
  });

  if (!hub) {
    notFound();
  }

  // Transform data for client component
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    private: hub.private,
    welcomeMessage: hub.welcomeMessage,
    rules: hub.rules,
    bannerUrl: hub.bannerUrl,
    iconUrl: hub.iconUrl,
    language: hub.language,
    nsfw: hub.nsfw,
    tags: hub.tags.map(tag => tag.name),
    connectionCount: hub._count.connections,
    isOwner,
    canEdit,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-900/20 via-gray-900/5 to-transparent" />
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />

      {/* Content */}
      <div className="relative">
        {/* Header */}
        <div className="border-b border-gray-800/50 bg-gray-950/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                  asChild
                >
                  <Link href={`/dashboard/hubs/${hubId}`}>
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Hub
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                    Edit {hub.name}
                  </h1>
                  <p className="text-gray-400 text-sm">
                    Customize your hub&apos;s appearance, settings, and content
                  </p>
                </div>
              </div>

              {/* Hub Stats */}
              <div className="hidden lg:flex items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-400" />
                  <span>{hubData.connectionCount} connections</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-green-400" />
                  <span>{hub.private ? "Private" : "Public"}</span>
                </div>
                {isOwner && (
                  <DeleteHubDialog hubId={hubId} hubName={hub.name} />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-800/50 bg-gray-950/60 backdrop-blur-sm">
          <div className="container mx-auto px-4">
            <HubNavigationTabs
              hubId={hubId}
              currentTab="edit"
              canModerate={permissionLevel >= PermissionLevel.MODERATOR}
              canEdit={canEdit}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {/* Feature Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-gradient-to-br from-purple-500/10 to-indigo-600/10 border border-purple-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Edit3 className="h-5 w-5 text-purple-400" />
                  </div>
                  <h3 className="font-semibold text-white">Basic Settings</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Update your hub&apos;s name, description, privacy settings, and community rules
                </p>
              </div>

              <div className="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 border border-blue-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Palette className="h-5 w-5 text-blue-400" />
                  </div>
                  <h3 className="font-semibold text-white">Appearance</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Customize your hub&apos;s visual identity with banners, icons, and branding
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-500/10 to-emerald-600/10 border border-green-500/20 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Globe className="h-5 w-5 text-green-400" />
                  </div>
                  <h3 className="font-semibold text-white">Discovery</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Manage tags, language settings, and content preferences for better discoverability
                </p>
              </div>
            </div>

            {/* Edit Form */}
            <HubEditForm hubData={hubData} />
          </div>
        </div>
      </div>
    </div>
  );
}
